<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格换行测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px 16px;
            text-align: left;
            vertical-align: top;
            /* 确保表格单元格内的换行正确显示 */
            white-space: pre-wrap;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        /* 表格内的硬换行样式 */
        .milkdown-hardbreak {
            display: inline;
            white-space: pre;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
        }
        
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>表格换行测试</h1>
    
    <div class="test-section">
        <div class="test-title">测试1: 使用 \n 字符的硬换行</div>
        <table>
            <thead>
                <tr>
                    <th>列1</th>
                    <th>列2</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>第一行第一列<span class="milkdown-hardbreak" contenteditable="false">
</span>内容换行</td>
                    <td>第一行第二列<span class="milkdown-hardbreak" contenteditable="false">
</span>内容换行</td>
                </tr>
                <tr>
                    <td>第二行第一列<span class="milkdown-hardbreak" contenteditable="false">
</span>内容换行</td>
                    <td>第二行第二列<span class="milkdown-hardbreak" contenteditable="false">
</span>内容换行</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <div class="test-title">测试2: 使用 br 标签的换行</div>
        <table>
            <thead>
                <tr>
                    <th>列1</th>
                    <th>列2</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>第一行第一列<br>内容换行</td>
                    <td>第一行第二列<br>内容换行</td>
                </tr>
                <tr>
                    <td>第二行第一列<br>内容换行</td>
                    <td>第二行第二列<br>内容换行</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <div class="test-title">测试3: 使用 pre-wrap 的普通文本换行</div>
        <table>
            <thead>
                <tr>
                    <th>列1</th>
                    <th>列2</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>第一行第一列
内容换行</td>
                    <td>第一行第二列
内容换行</td>
                </tr>
                <tr>
                    <td>第二行第一列
内容换行</td>
                    <td>第二行第二列
内容换行</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <script>
        // 测试脚本，用于验证不同换行方式的效果
        console.log('表格换行测试页面已加载');
        
        // 检查硬换行元素
        const hardbreaks = document.querySelectorAll('.milkdown-hardbreak');
        console.log('找到硬换行元素数量:', hardbreaks.length);
        
        hardbreaks.forEach((el, index) => {
            console.log(`硬换行元素 ${index + 1}:`, {
                textContent: JSON.stringify(el.textContent),
                innerHTML: el.innerHTML,
                computedStyle: {
                    display: getComputedStyle(el).display,
                    whiteSpace: getComputedStyle(el).whiteSpace
                }
            });
        });
    </script>
</body>
</html>
